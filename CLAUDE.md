# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a LangGraph-based ReAct agent application with a Next.js frontend and FastAPI backend. The system uses a multi-agent orchestrator pattern where a supervisor routes tasks to specialized workers (search_worker, code_worker) for processing user requests.

### Architecture

- **Backend** (`ration-agent/backend/`): FastAPI server with LangGraph orchestrator system
  - `core/agent.py`: Core orchestrator with shared PostgreSQL connection management
  - `agents/nodes.py`: Agent node implementations (supervisor, search_worker, code_worker)
  - `main.py`: FastAPI application entry point
  - `api/routes.py`: API routes and SSE streaming endpoints
  - `services/session_manager.py`: Session and conversation management
  - `utils/tools.py`: Code execution and file operation tools
  - `utils/tools.py`: Web search and information gathering tools (search_tools functions)
  - `prompts/`: Jinja2 templates for agent prompts

- **Frontend** (`ration-agent/frontend/`): Next.js 15 with React 19 chat interface
  - Real-time chat using Server-Sent Events (SSE)
  - Conversation history and session management
  - File upload capabilities for data analysis
  - Markdown rendering with syntax highlighting

- **Database**: PostgreSQL with pgvector extension for conversation persistence

## Development Commands

### Backend (Python with uv)
```bash
cd ration-agent/backend
uv run main.py                    # Start development server
uv run start_server.py           # Alternative start command
uv sync                          # Install dependencies
```

### Frontend (Node.js)
```bash
cd ration-agent/frontend
npm run dev                      # Start development server
npm run build                    # Build for production
npm run start                    # Start production server
npm run lint                     # Run ESLint
```

### Database
```bash
docker-compose up -d             # Start PostgreSQL container
```

## Key Technical Details

### Agent System
- Uses LangGraph StateGraph with OrchestratorState for multi-agent coordination
- Shared AsyncConnectionPool for PostgreSQL checkpoint persistence
- Streaming response parsing with `<user>` and `<action>` blocks
- Session-based conversation threading

### API Endpoints
- `POST /chat/stream/{session_id}`: Main chat endpoint with SSE streaming
- `GET /sessions/{session_id}/history`: Get conversation history
- `POST /sessions/create`: Create new session
- `DELETE /sessions/{session_id}`: Delete sessions
- `POST /files/upload/{session_id}`: File upload for analysis

### Environment Variables
Required for backend:
- `POSTGRES_HOST`, `POSTGRES_PORT`, `POSTGRES_DB`, `POSTGRES_USER`, `POSTGRES_PASSWORD`
- `OPENROUTER_MODEL`, `OPENROUTER_API_KEY`

### File Upload System
- Stores uploaded files in `backend/files/{session_id}/`
- Supports Excel files for data analysis with pandas/openpyxl
- Automatic file cleanup on session deletion

### Message Parsing
- Uses StreamingResponseParser for handling `<user>` and `<action>` blocks
- Unified message parser for consistent formatting across streaming and history
- Role transitions tracked via action routing decisions