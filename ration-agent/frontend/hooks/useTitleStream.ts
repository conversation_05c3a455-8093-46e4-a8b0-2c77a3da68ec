'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

interface UseTitleStreamProps {
  sessionId: string
  endpoint?: string
  onTitleUpdate?: (sessionId: string, title: string) => void
}

interface UseTitleStreamReturn {
  currentTitle: string | null
  isConnected: boolean
  error: string | null
  startTitleStream: () => void
}

export function useTitleStream({ 
  sessionId, 
  endpoint = 'http://localhost:8000',
  onTitleUpdate 
}: UseTitleStreamProps): UseTitleStreamReturn {
  const [currentTitle, setCurrentTitle] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const eventSourceRef = useRef<EventSource | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }
    if (abortControllerRef.current) {
      try {
        abortControllerRef.current.abort()
      } catch (error) {
        // Ignore abort errors during cleanup
        console.debug('Error during abort cleanup:', error)
      }
      abortControllerRef.current = null
    }
    setIsConnected(false)
  }, [])

  const connectToTitleStream = useCallback(() => {
    if (!sessionId) return

    cleanup()

    try {
      // Create abort controller
      abortControllerRef.current = new AbortController()
      
      // Connect to title stream endpoint
      const url = `${endpoint}/sessions/${sessionId}/title/stream`
      console.log('Connecting to title stream:', url)
      
      // Use fetch with SSE for better control
      const fetchPromise = fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        signal: abortControllerRef.current.signal,
      })
      
      fetchPromise.then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        if (!response.body) {
          throw new Error('No response body for title stream')
        }

        setIsConnected(true)
        setError(null)

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            let eventType = 'message'
            let eventData = ''

            for (const line of lines) {
              if (line.startsWith('event:')) {
                eventType = line.slice(6).trim()
              } else if (line.startsWith('data:')) {
                eventData = line.slice(5).trim()
              } else if (line === '' && eventData) {
                // End of event, process it
                try {
                  const data = JSON.parse(eventData)
                  
                  switch (eventType) {
                    case 'connected':
                      console.log('Title stream connected:', data)
                      if (data.current_title && data.current_title !== 'New Conversation') {
                        setCurrentTitle(data.current_title)
                      }
                      break
                      
                    case 'title_ready':
                      console.log('Title ready:', data)
                      setCurrentTitle(data.title)
                      if (onTitleUpdate) {
                        onTitleUpdate(data.session_id, data.title)
                      }
                      // Close connection after receiving title
                      cleanup()
                      return
                      
                    case 'title_timeout':
                      console.log('Title generation timed out:', data)
                      setError('Title generation timed out')
                      cleanup()
                      return
                      
                    case 'error':
                      console.error('Title stream error:', data)
                      setError(data.message || 'Title generation error')
                      cleanup()
                      return
                      
                    default:
                      console.log('Unknown title event:', eventType, data)
                  }
                } catch (parseError) {
                  console.error('Error parsing title event:', parseError, 'Raw data:', eventData)
                }
                
                eventType = 'message'
                eventData = ''
              }
            }
          }
        } finally {
          reader.releaseLock()
          cleanup()
        }
      })
      .catch((error) => {
        if (error.name === 'AbortError') {
          console.log('Title stream aborted')
          return
        }
        console.error('Title stream error:', error)
        setError(error.message)
        setIsConnected(false)
      })
      .finally(() => {
        // Ensure cleanup happens regardless of success or failure
        cleanup()
      })

    } catch (error: any) {
      console.error('Error connecting to title stream:', error)
      setError(error.message)
      setIsConnected(false)
    }
  }, [sessionId, endpoint, onTitleUpdate, cleanup])

  // Don't auto-connect - wait for manual trigger
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    currentTitle,
    isConnected,
    error,
    startTitleStream: connectToTitleStream,
  }
}