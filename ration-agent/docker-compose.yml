services:
  postgres:
    image: pgvector/pgvector:0.8.0-pg17
    container_name: ration_agent_postgres
    environment:
      POSTGRES_DB: ration_agent
      POSTGRES_USER: ration_user
      POSTGRES_PASSWORD: ration_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data: